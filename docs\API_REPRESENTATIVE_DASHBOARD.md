# Representative Dashboard Stats API

This endpoint provides daily statistics for representatives to display on their mobile dashboard.

## Important Changes to Appointments Endpoints

### Updated Behavior for Appointments List
- **`GET /api/v1/appointments`** and **`GET /api/v1/appointments/my-appointments`** now return only **upcoming appointments** by default
- To include past appointments, add the query parameter `include_past=true`
- All appointments now include purchase relationships to check appointment status
- Appointments are ordered by date (upcoming first)

### Appointment Status Field
Each appointment now includes a `status` field that indicates:
- `"pending"`: No purchases exist for this appointment
- `"completed"`: Appointment has purchases with status 'purchased'
- `"no_purchase"`: Appointment has purchases but none with status 'purchased'

## Dashboard Stats Endpoint

```
GET /api/v1/appointments/representative-dashboard-stats
```

## Authentication

- **Required**: Yes
- **Type**: <PERSON><PERSON> (Sanctum)
- **Role**: Only `representative` users can access this endpoint

## Request

### Headers
```
Authorization: Bearer {token}
Content-Type: application/json
```

### Parameters
None required. The endpoint automatically calculates stats for the current authenticated representative for today's date.

## Response

### Success Response (200)

```json
{
    "success": true,
    "message": "Representative dashboard stats retrieved successfully",
    "data": {
        "todays_appointments": 5,
        "todays_realized_appointments": 3,
        "todays_revenue": 450.75,
        "todays_benefit": 150.25,
        "realization_rate": 60.0,
        "date": "2025-08-20"
    }
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `todays_appointments` | integer | Total number of appointments scheduled for today |
| `todays_realized_appointments` | integer | Number of appointments that resulted in purchases |
| `todays_revenue` | float | Total revenue (chiffre d'affaires) from today's purchases |
| `todays_benefit` | float | Total profit/benefit from today's purchases |
| `realization_rate` | float | Percentage of appointments that resulted in purchases (0-100) |
| `date` | string | Date for which the stats are calculated (YYYY-MM-DD) |

### Error Responses

#### 401 Unauthorized
```json
{
    "message": "Unauthenticated."
}
```

#### 403 Forbidden
```json
{
    "success": false,
    "message": "Only representatives can access dashboard stats"
}
```

## Usage Examples

### cURL Example
```bash
curl -X GET \
  'https://your-domain.com/api/v1/appointments/representative-dashboard-stats' \
  -H 'Authorization: Bearer your-token-here' \
  -H 'Content-Type: application/json'
```

### JavaScript/Fetch Example
```javascript
const response = await fetch('/api/v1/appointments/representative-dashboard-stats', {
    method: 'GET',
    headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    }
});

const data = await response.json();
console.log(data.data); // Dashboard stats
```

### React Native Example
```javascript
const getDashboardStats = async () => {
    try {
        const response = await fetch('/api/v1/appointments/representative-dashboard-stats', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${userToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            setDashboardStats(result.data);
        }
    } catch (error) {
        console.error('Error fetching dashboard stats:', error);
    }
};
```

## Business Logic

1. **Today's Appointments**: Counts all appointments where `dateTime` is today and `representative_id` matches the authenticated user
2. **Realized Appointments**: Counts appointments that have at least one purchase with `status = 'purchased'`
3. **Revenue**: Sums the `resale_price` from all purchases with `status = 'purchased'` for today's appointments
4. **Benefit**: Sums the `benefit` field from all purchases with `status = 'purchased'` for today's appointments
5. **Realization Rate**: Calculated as `(realized_appointments / total_appointments) * 100`

## Mobile App Integration Example

Here's how you might integrate this endpoint into a React Native dashboard component:

```javascript
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, RefreshControl, ScrollView } from 'react-native';

const RepresentativeDashboard = ({ userToken }) => {
    const [stats, setStats] = useState(null);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);

    const fetchDashboardStats = async () => {
        try {
            const response = await fetch('/api/v1/appointments/representative-dashboard-stats', {
                headers: {
                    'Authorization': `Bearer ${userToken}`,
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();
            if (result.success) {
                setStats(result.data);
            }
        } catch (error) {
            console.error('Error fetching stats:', error);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    useEffect(() => {
        fetchDashboardStats();
    }, []);

    const onRefresh = () => {
        setRefreshing(true);
        fetchDashboardStats();
    };

    if (loading) return <Text>Loading...</Text>;

    return (
        <ScrollView
            style={styles.container}
            refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        >
            <Text style={styles.title}>Today's Performance</Text>

            <View style={styles.statsContainer}>
                <View style={styles.statCard}>
                    <Text style={styles.statNumber}>{stats?.todays_appointments || 0}</Text>
                    <Text style={styles.statLabel}>Appointments</Text>
                </View>

                <View style={styles.statCard}>
                    <Text style={styles.statNumber}>{stats?.todays_realized_appointments || 0}</Text>
                    <Text style={styles.statLabel}>Realized</Text>
                </View>

                <View style={styles.statCard}>
                    <Text style={styles.statNumber}>€{stats?.todays_revenue?.toFixed(2) || '0.00'}</Text>
                    <Text style={styles.statLabel}>Revenue</Text>
                </View>

                <View style={styles.statCard}>
                    <Text style={styles.statNumber}>{stats?.realization_rate?.toFixed(1) || 0}%</Text>
                    <Text style={styles.statLabel}>Success Rate</Text>
                </View>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: { flex: 1, padding: 16 },
    title: { fontSize: 24, fontWeight: 'bold', marginBottom: 16 },
    statsContainer: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' },
    statCard: { width: '48%', backgroundColor: '#f5f5f5', padding: 16, borderRadius: 8, marginBottom: 16 },
    statNumber: { fontSize: 24, fontWeight: 'bold', color: '#333' },
    statLabel: { fontSize: 14, color: '#666', marginTop: 4 }
});
```

## Notes

- All monetary values are returned as floats
- The `date` field shows the date for which statistics are calculated (always today)
- If no appointments exist for today, all counts will be 0
- The realization rate is rounded to 1 decimal place
- Only appointments scheduled for today (based on `dateTime` field) are included in calculations
- Consider implementing pull-to-refresh functionality for real-time updates
- Cache the data appropriately to avoid excessive API calls

## Updated Appointments Endpoints

### Get Appointments List
```
GET /api/v1/appointments
GET /api/v1/appointments/my-appointments
```

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `include_past` | boolean | `false` | Include past appointments in the results |
| `per_page` | integer | `15` | Number of results per page |
| `assistant_id` | integer | - | Filter by assistant ID |
| `representative_id` | integer | - | Filter by representative ID |
| `source` | string | - | Filter by source (`outbound`, `leboncoin`) |
| `appointment_type` | string | - | Filter by type (`announced`, `not_announced`) |
| `date_from` | date | - | Filter appointments from this date |
| `date_to` | date | - | Filter appointments to this date |

#### Examples

**Get upcoming appointments only (default):**
```javascript
fetch('/api/v1/appointments/my-appointments', {
    headers: { 'Authorization': `Bearer ${token}` }
});
```

**Get all appointments including past ones:**
```javascript
fetch('/api/v1/appointments/my-appointments?include_past=true', {
    headers: { 'Authorization': `Bearer ${token}` }
});
```

#### Response Structure
```json
{
    "success": true,
    "message": "User appointments retrieved successfully",
    "data": {
        "data": [
            {
                "id": 1,
                "client_name": "John Doe",
                "date_time": "2025-08-21T10:00:00.000Z",
                "status": "pending",
                "purchases": [],
                "assistant": {...},
                "representative": {...}
            }
        ],
        "pagination": {...}
    }
}
```

### Appointment Status Values
- **`"pending"`**: No purchases recorded yet
- **`"completed"`**: Has successful purchases (status = 'purchased')
- **`"no_purchase"`**: Has purchase records but no successful purchases
